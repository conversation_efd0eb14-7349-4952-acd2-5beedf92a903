# OpenRouter Model Configuration

This project supports configurable OpenRouter LLM models for each feature/page via environment variables. This allows you to customize which AI model is used for different functionalities without changing the code.

## Setup

1. Copy `.env.local.example` to `.env.local`
2. Set your `OPENROUTER_API_KEY`
3. Customize the model for each feature by setting the corresponding environment variable

## Available Environment Variables

Each OpenRouter endpoint can be configured with a specific model using these environment variables:

### <PERSON> Analysis
- `OPENROUTER_MODEL_CLAYTON` - <PERSON> JTBD analysis page
- `OPENROUTER_MODEL_CLAYTON_2` - Enhanced <PERSON> analysis page

### Gary Vaynerchuk Analysis
- `OPENROUTER_MODEL_GARY` - Gary <PERSON> brand building analysis page
- `OPENROUTER_MODEL_GARY_2` - Enhanced Gary V analysis page

### Lei Jun Analysis
- `OPENROUTER_MODEL_IDEAL` - Business ideal analysis page (Lei Jun)
- `OPENROUTER_MODEL_LEIJUN_2` - Enhanced Le<PERSON> Jun analysis page

### Nancy Harhut Behavioral Science
- `OPENROUTER_MODEL_NANCY_HARHUT` - <PERSON>t behavioral science analysis
- `OPENROUTER_MODEL_NANCY_HARHUT_2` - Nancy Harhut behavioral science playbook

### Video Features
- `OPENROUTER_MODEL_VIDEO_CONCEPTS` - Video Science concepts generation
- `OPENROUTER_MODEL_VIDEO_SCRIPTS` - Video scripts generation

### Marketing and General Features
- `OPENROUTER_MODEL_MARKETING_MESSAGES` - Marketing messages generation
- `OPENROUTER_MODEL_REVIEWS` - App reviews analysis
- `OPENROUTER_MODEL_CHAT` - AI chatbox functionality

## Popular Model Options

You can use any model available on OpenRouter. Here are some popular options:

### Fast and Cost-Effective
- `moonshotai/kimi-k2` (default)
- `anthropic/claude-3.5-haiku`
- `google/gemini-2.0-flash-001`

### High Performance
- `anthropic/claude-3.5-sonnet`
- `openai/gpt-4o`
- `google/gemini-2.0-flash-thinking-exp`

### Specialized Models
- `meta-llama/llama-3.3-70b-instruct`
- `qwen/qwen-2.5-72b-instruct`
- `deepseek/deepseek-chat`

## Example Configuration

```bash
# .env.local
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Use Claude Haiku for all analysis features
OPENROUTER_MODEL_CLAYTON=anthropic/claude-3.5-haiku
OPENROUTER_MODEL_GARY=anthropic/claude-3.5-haiku
OPENROUTER_MODEL_NANCY_HARHUT=anthropic/claude-3.5-haiku

# Use Gemini for video features
OPENROUTER_MODEL_VIDEO_CONCEPTS=google/gemini-2.0-flash-001
OPENROUTER_MODEL_VIDEO_SCRIPTS=google/gemini-2.0-flash-001

# Use Kimi for everything else (default)
OPENROUTER_MODEL_CHAT=moonshotai/kimi-k2
OPENROUTER_MODEL_REVIEWS=moonshotai/kimi-k2
```

## Default Behavior

If an environment variable is not set, the system will default to `moonshotai/kimi-k2` for that feature.

## Notes

- The `analyze-video-science` endpoint uses the direct Google Gemini API (not OpenRouter) for video processing capabilities, so it's not configurable via these environment variables.
- Changes to environment variables require a server restart to take effect.
- Make sure the model you specify is available on OpenRouter and compatible with your API key.

## Testing Different Models

You can easily test different models for specific features by:

1. Setting the environment variable for that feature
2. Restarting your development server
3. Testing the feature to see how the different model performs

This allows you to optimize model selection based on performance, cost, and quality for each specific use case.
