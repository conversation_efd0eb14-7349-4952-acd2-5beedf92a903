# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# OpenRouter LLM Models for Each Feature
# You can customize the model for each page/feature by changing these values
# Available models: moonshotai/kimi-k2, anthropic/claude-3.5-haiku, google/gemini-2.0-flash-001, etc.

# Clayton Christensen Analysis Pages
OPENROUTER_MODEL_CLAYTON=moonshotai/kimi-k2
OPENROUTER_MODEL_CLAYTON_2=moonshotai/kimi-k2

# Gary Vaynerchuk Analysis Pages
OPENROUTER_MODEL_GARY=moonshotai/kimi-k2
OPENROUTER_MODEL_GARY_2=moonshotai/kimi-k2

# Lei Jun Analysis Pages
OPENROUTER_MODEL_IDEAL=moonshotai/kimi-k2
OPENROUTER_MODEL_LEIJUN_2=moonshotai/kimi-k2

# Nancy Harhut Behavioral Science Pages
OPENROUTER_MODEL_NANCY_HARHUT=moonshotai/kimi-k2
OPENROUTER_MODEL_NANCY_HARHUT_2=moonshotai/kimi-k2

# Video Features
OPENROUTER_MODEL_VIDEO_CONCEPTS=moonshotai/kimi-k2
OPENROUTER_MODEL_VIDEO_SCRIPTS=moonshotai/kimi-k2

# Marketing and General Features
OPENROUTER_MODEL_MARKETING_MESSAGES=moonshotai/kimi-k2
OPENROUTER_MODEL_REVIEWS=moonshotai/kimi-k2
OPENROUTER_MODEL_CHAT=moonshotai/kimi-k2

# Other API Keys (keep existing)
GEMINI_API_KEY=your_gemini_api_key_here
