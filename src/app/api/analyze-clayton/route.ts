import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessIdeal, description, messages } = await request.json();

    if (!businessIdeal || !description) {
      return NextResponse.json({ message: 'Business ideal and description are required.' }, { status: 400 });
    }

    const bookContentPath = path.join(process.cwd(), 'public', 'book_summary.md');
    let bookContent = '';
    try {
        bookContent = await fs.readFile(bookContentPath, 'utf-8');
    } catch (readError) {
      console.error('Failed to read book_summary.md:', readError);
      return NextResponse.json({ message: 'Failed to load book content for analysis.' }, { status: 500 });
    }

    const prompt = `
      You are a world-class business strategist, channeling the wisdom of <PERSON>. Your analysis is sharp, actionable, and rooted in the principles from "Competing Against Luck."

      A user has presented a business concept. Your mission is to forge it into a market-leading product by applying the "Jobs to Be Done" theory.

      **Reference Principles: <PERSON>'s "Competing Against Luck"**
      ${bookContent}

      ---

      **User's Business Concept**
      - **Ideal:** "${businessIdeal}"
      - **Description:** "${description}"

      ---

      **Your Task: Create a Strategic Blueprint Based on "Jobs to Be Done"**

      Produce a detailed, yet precise, strategic blueprint in well-formatted markdown. Follow this structure exactly:

      **1. Executive Summary & Core Job Alignment**
         - Briefly summarize the business ideal.
         - Identify the core "Job to Be Done" (JTBD) that this business ideal aims to fulfill for its customers.

      **2. Fundamental JTBD Analysis: Strengths, Weaknesses, and Solutions**
         - **Fundamental Strengths (against JTBD principles):** List 10 key strengths of the business concept when evaluated against Jobs to Be Done principles. Provide a detailed explanation for each strength, elaborating on *why* it is a strength and its positive implications for fulfilling the Job to Be Done.
         - **Fundamental Weaknesses (against JTBD principles):** List 10 key weaknesses of the business concept when evaluated against Jobs to Be Done principles. For each weakness, immediately follow with a concrete, actionable solution. Provide a detailed explanation for each weakness, elaborating on *why* it is a weakness and its negative implications for fulfilling the Job to Be Done. For each weakness, provide a comprehensive, actionable solution that delves into *how* to address it, including specific steps, strategies, or shifts in approach.

      **3. "Job-to-Be-Done" Viability Score (out of 100)**
         - Provide a brutally honest JTBD viability score.
         - **Job Clarity (25 pts):** How well is the underlying job defined and understood? Justify the score.
         - **Solution Fit (25 pts):** How effectively does the proposed ideal "hire" for this job? Justify the score.
         - **Adoption Barriers (25 pts):** Analysis of "anxieties" and "habits" that create resistance. Justify the score.
         - **Growth Potential within the Job (25 pts):** Outline opportunities for expanding within the identified job space. Justify the score.
         - **Verdict & Risks:** Deliver a final verdict, outlining the biggest JTBD-related risks and opportunities.

      **4. Actionable "Quick Wins" for Job Fulfillment**
         - List 3-5 immediate, high-impact actions the user can take to better fulfill the identified Job to Be Done.

      **5. The "Ultimate Product" Feature Roadmap (50 Features) based on JTBD**
         - Craft a roadmap designed to create a product that excels at getting the job done.
         - **Functional (15):** Features directly related to the core task completion.
         - **Emotional (10):** Features that address the emotional aspects of the job.
         - **Social (10):** Features that address the social aspects of the job.
         - **Circumstantial (5):** Features tailored to specific contexts of use.
         - **Pain Point Resolution (5):** Features that directly alleviate current frustrations.
         - **Delight & Over-serving (5):** Features that unexpectedly improve the job experience.
         - *For each feature, specify its name, a brief description, the JTBD dimension it addresses (Functional, Emotional, Social, Circumstantial, Pain Point, Delight), and its user impact.*

      **6. Strategic Recommendations from "Competing Against Luck" (Actionable for Web Developers)**
         - **Principle-by-Principle Analysis:** For each relevant JTBD principle (e.g., understanding the "circumstances," identifying "non-consumption"), provide a concise analysis of the ideal's current state and concrete, *actionable recommendations for web developers*.
            - **For each recommendation, explicitly state what to ADD (new features, UI/UX elements, backend logic, data collection) and what to REMOVE/REFACTOR (existing features, unnecessary complexity, confusing flows) from the business ideal's implementation.**
            - **Example:** Instead of "Stop thinking 'diabetes image scanning app.' Start thinking 'confidence builder for diabetic eaters.' Every design decision, every feature, every marketing message must answer: 'How does this feature contribute to the user's progress of confidently and effortlessly making healthier food choices?' Focus on the 'causal mechanism' of why users would choose this over guessing or traditional carb counting."
            - **Your output should be like this example, providing concrete, web-development-specific suggestions.**
         - **Pricing Strategy:** Propose a simple, high-value pricing model, based on the value delivered in getting the job done, with considerations for subscription models, freemium, or one-time purchases relevant to web services.
         - **Ecosystem Development (5 Components):** Suggest 5 complementary ecosystem components that help customers get the job done more completely. For each, describe its purpose, how it enhances the core ideal, and its implementation priority, including how web developers would integrate or build these components.

      Your analysis must be insightful, practical, and transform the user's concept into a potential success story by focusing on the "Jobs to Be Done."
    `;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
    const OPENROUTER_MODEL = process.env.OPENROUTER_MODEL_CLAYTON || 'moonshotai/kimi-k2';

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App',
      },
      body: JSON.stringify({
        model: OPENROUTER_MODEL,
        messages: [
          ...(messages || []),
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true,
      }),
    });

    if (!openRouterResponse.ok) {
      let errorData;
      try {
        errorData = await openRouterResponse.json();
      } catch (jsonError) {
        errorData = await openRouterResponse.text();
      }

      const errorMessage = typeof errorData === 'object' && errorData !== null && 'message' in errorData
        ? errorData.message
        : String(errorData);

      console.error('OpenRouter API error:', errorMessage);
      return NextResponse.json({ message: `OpenRouter API error: ${errorMessage}` }, { status: openRouterResponse.status });
    }

    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });

            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const jsonStr = line.substring(6);
                if (jsonStr === '[DONE]') {
                  controller.close();
                  return;
                }
                try {
                  const data = JSON.parse(jsonStr);
                  const content = data.choices[0]?.delta?.text || data.choices[0]?.delta?.content || '';
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (parseError) {
                  console.error('Error parsing stream chunk:', parseError);
                  controller.enqueue(new TextEncoder().encode(`[PARSE_ERROR] ${line}\n`));
                }
              } else if (line.trim() !== '') {
                console.warn('Received non-data line in stream:', line);
              }
            }
          }
        } catch (readError) {
          console.error('Error reading stream:', readError);
          controller.error(readError);
        } finally {
          controller.close();
        }
      },
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      },
    });

  } catch (error: any) {
    console.error('API route error:', error);
    return NextResponse.json({ message: error.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}
